import { useCallback, useRef, useEffect } from 'react';
import { useMarkMessagesAsReadMutation } from '@/store/api/chatApiSlice';

interface UseMarkAsReadOptions {
  conversationId: string | null;
  userId: string;
  debounceMs?: number;
}

export const useMarkAsRead = ({
  conversationId,
  userId,
  debounceMs = 1000,
}: UseMarkAsReadOptions) => {
  const [markAsRead] = useMarkMessagesAsReadMutation();
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  const debouncedMarkAsRead = useCallback(() => {
    if (!conversationId) return;

    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      markAsRead(conversationId).catch(err =>
        console.error('Error marking messages as read:', err)
      );
    }, debounceMs);
  }, [conversationId, markAsRead, debounceMs]);

  const markAsReadImmediately = useCallback(() => {
    if (!conversationId) return;

    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    markAsRead(conversationId).catch(err =>
      console.error('Error marking messages as read:', err)
    );
  }, [conversationId, markAsRead]);

  const setupMessageObserver = useCallback(
    (messageElements: NodeListOf<Element>) => {
      if (!conversationId || !userId) return;

      if (observerRef.current) {
        observerRef.current.disconnect();
      }

      observerRef.current = new IntersectionObserver(
        entries => {
          let shouldMarkAsRead = false;

          entries.forEach(entry => {
            if (entry.isIntersecting) {
              const messageElement = entry.target as HTMLElement;
              const senderId = messageElement.dataset.senderId;

              if (senderId && senderId !== userId) {
                shouldMarkAsRead = true;
              }
            }
          });

          if (shouldMarkAsRead) {
            debouncedMarkAsRead();
          }
        },
        {
          threshold: 0.5,
          rootMargin: '0px 0px -50px 0px',
        }
      );

      messageElements.forEach(element => {
        observerRef.current?.observe(element);
      });
    },
    [conversationId, userId, debouncedMarkAsRead]
  );

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, []);

  return {
    markAsReadImmediately,
    debouncedMarkAsRead,
    setupMessageObserver,
  };
};
