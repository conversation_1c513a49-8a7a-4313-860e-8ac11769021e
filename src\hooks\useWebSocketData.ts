import { useState, useEffect, useCallback, useRef } from 'react';
import useWebSocket from './useWebSocket';
import {
  Conversation,
  Message,
  ConversationsResponse,
  ConversationResponse,
  MessagesResponse,
  SendMessageResponse,
  GetConversationsParams,
  GetMessagesParams,
  CreateConversationRequest,
} from '@/store/api/chatApiSlice';

interface QueryState<T> {
  data?: T;
  isLoading: boolean;
  error?: any;
  isSuccess: boolean;
  isError: boolean;
}

interface MutationState<T> {
  data?: T;
  isLoading: boolean;
  error?: any;
  isSuccess: boolean;
  isError: boolean;
}

interface UseWebSocketDataOptions {
  url: string;
  token: string;
  userId: string;
  userType: 'nurse' | 'patient';
  userName: string;
  enabled?: boolean;
}

export const useWebSocketData = (options: UseWebSocketDataOptions) => {
  const {
    status: wsStatus,
    requestConversations,
    requestConversation,
    requestMessages,
    createConversation: wsCreateConversation,
    markAsRead: wsMarkAsRead,
    updateStatus: wsUpdateStatus,
    sendTextMessage,
    sendTypingIndicator,
    sendReadReceipt,
    joinConversation,
    leaveConversation,
    typingUsers,
  } = useWebSocket({
    ...options,
    onMessage: (message) => {
      // Handle real-time message updates
      if (message.type === 'TEXT_MESSAGE' && message.conversationId) {
        // Update messages cache
        setMessagesCache(prev => {
          const conversationMessages = prev[message.conversationId!] || { messages: [], pagination: { page: 1, limit: 50, total: 0, totalPages: 0 } };
          const newMessage: Message = {
            id: `${Date.now()}_${Math.random()}`,
            conversationId: message.conversationId!,
            senderId: message.senderId!,
            senderType: message.senderType!,
            senderName: message.senderName!,
            content: message.content!,
            type: 'text',
            status: 'sent',
            timestamp: message.timestamp!,
            metadata: message.metadata,
          };
          
          return {
            ...prev,
            [message.conversationId!]: {
              ...conversationMessages,
              messages: [...conversationMessages.messages, newMessage],
            },
          };
        });
        
        // Update conversations cache with last message
        setConversationsCache(prev => {
          if (!prev) return prev;
          
          const updatedConversations = prev.conversations.map(conv => 
            conv.id === message.conversationId 
              ? { 
                  ...conv, 
                  lastMessage: message.content,
                  lastMessageTime: message.timestamp,
                  updatedAt: message.timestamp!,
                }
              : conv
          );
          
          return {
            ...prev,
            conversations: updatedConversations,
          };
        });
      }
    },
  });

  // Cache state
  const [conversationsCache, setConversationsCache] = useState<ConversationsResponse | null>(null);
  const [conversationCache, setConversationCache] = useState<Record<string, ConversationResponse>>({});
  const [messagesCache, setMessagesCache] = useState<Record<string, MessagesResponse>>({});

  // Query states
  const [conversationsQuery, setConversationsQuery] = useState<QueryState<ConversationsResponse>>({
    isLoading: false,
    isSuccess: false,
    isError: false,
  });

  const [conversationQueries, setConversationQueries] = useState<Record<string, QueryState<ConversationResponse>>>({});
  const [messagesQueries, setMessagesQueries] = useState<Record<string, QueryState<MessagesResponse>>>({});

  // Mutation states
  const [createConversationMutation, setCreateConversationMutation] = useState<MutationState<ConversationResponse>>({
    isLoading: false,
    isSuccess: false,
    isError: false,
  });

  const [sendMessageMutation, setSendMessageMutation] = useState<MutationState<SendMessageResponse>>({
    isLoading: false,
    isSuccess: false,
    isError: false,
  });

  const [markAsReadMutation, setMarkAsReadMutation] = useState<MutationState<{ success: boolean }>>({
    isLoading: false,
    isSuccess: false,
    isError: false,
  });

  const [updateStatusMutation, setUpdateStatusMutation] = useState<MutationState<ConversationResponse>>({
    isLoading: false,
    isSuccess: false,
    isError: false,
  });

  // Query functions
  const useGetConversationsQuery = useCallback((params?: GetConversationsParams, options?: { skip?: boolean }) => {
    const queryKey = JSON.stringify(params || {});
    const skipQuery = options?.skip || false;

    useEffect(() => {
      if (skipQuery || !wsStatus.connected) return;

      const fetchData = async () => {
        setConversationsQuery(prev => ({ ...prev, isLoading: true, isError: false }));
        
        try {
          const response = await requestConversations(params);
          const formattedResponse: ConversationsResponse = {
            success: true,
            conversations: response.conversations || [],
            pagination: response.pagination || { page: 1, limit: 50, total: 0, totalPages: 0 },
          };
          
          setConversationsCache(formattedResponse);
          setConversationsQuery({
            data: formattedResponse,
            isLoading: false,
            isSuccess: true,
            isError: false,
          });
        } catch (error) {
          setConversationsQuery({
            isLoading: false,
            isSuccess: false,
            isError: true,
            error,
          });
        }
      };

      fetchData();
    }, [queryKey, skipQuery, wsStatus.connected]);

    return {
      ...conversationsQuery,
      data: conversationsCache,
      refetch: () => {
        if (!wsStatus.connected) return Promise.resolve();
        return requestConversations(params).then(response => {
          const formattedResponse: ConversationsResponse = {
            success: true,
            conversations: response.conversations || [],
            pagination: response.pagination || { page: 1, limit: 50, total: 0, totalPages: 0 },
          };
          setConversationsCache(formattedResponse);
          return formattedResponse;
        });
      },
    };
  }, [conversationsQuery, conversationsCache, requestConversations, wsStatus.connected]);

  const useGetConversationQuery = useCallback((conversationId: string, options?: { skip?: boolean }) => {
    const skipQuery = options?.skip || !conversationId;
    const queryState = conversationQueries[conversationId] || {
      isLoading: false,
      isSuccess: false,
      isError: false,
    };

    useEffect(() => {
      if (skipQuery || !wsStatus.connected) return;

      const fetchData = async () => {
        setConversationQueries(prev => ({
          ...prev,
          [conversationId]: { ...prev[conversationId], isLoading: true, isError: false }
        }));
        
        try {
          const response = await requestConversation(conversationId);
          const formattedResponse: ConversationResponse = {
            success: true,
            conversation: response.conversation,
          };
          
          setConversationCache(prev => ({ ...prev, [conversationId]: formattedResponse }));
          setConversationQueries(prev => ({
            ...prev,
            [conversationId]: {
              data: formattedResponse,
              isLoading: false,
              isSuccess: true,
              isError: false,
            }
          }));
        } catch (error) {
          setConversationQueries(prev => ({
            ...prev,
            [conversationId]: {
              isLoading: false,
              isSuccess: false,
              isError: true,
              error,
            }
          }));
        }
      };

      fetchData();
    }, [conversationId, skipQuery, wsStatus.connected]);

    return {
      ...queryState,
      data: conversationCache[conversationId],
    };
  }, [conversationQueries, conversationCache, requestConversation, wsStatus.connected]);

  const useGetMessagesQuery = useCallback((params: GetMessagesParams, options?: { skip?: boolean }) => {
    const { conversationId, page = 1, limit = 50 } = params;
    const queryKey = `${conversationId}_${page}_${limit}`;
    const skipQuery = options?.skip || !conversationId;
    const queryState = messagesQueries[queryKey] || {
      isLoading: false,
      isSuccess: false,
      isError: false,
    };

    useEffect(() => {
      if (skipQuery || !wsStatus.connected) return;

      const fetchData = async () => {
        setMessagesQueries(prev => ({
          ...prev,
          [queryKey]: { ...prev[queryKey], isLoading: true, isError: false }
        }));

        try {
          const response = await requestMessages(conversationId, page, limit);
          const formattedResponse: MessagesResponse = {
            success: true,
            messages: response.messages || [],
            pagination: response.pagination || { page, limit, total: 0, totalPages: 0 },
          };

          setMessagesCache(prev => ({ ...prev, [conversationId]: formattedResponse }));
          setMessagesQueries(prev => ({
            ...prev,
            [queryKey]: {
              data: formattedResponse,
              isLoading: false,
              isSuccess: true,
              isError: false,
            }
          }));
        } catch (error) {
          setMessagesQueries(prev => ({
            ...prev,
            [queryKey]: {
              isLoading: false,
              isSuccess: false,
              isError: true,
              error,
            }
          }));
        }
      };

      fetchData();
    }, [queryKey, skipQuery, wsStatus.connected]);

    return {
      ...queryState,
      data: messagesCache[conversationId],
      refetch: () => {
        if (!wsStatus.connected) return Promise.resolve();
        return requestMessages(conversationId, page, limit).then(response => {
          const formattedResponse: MessagesResponse = {
            success: true,
            messages: response.messages || [],
            pagination: response.pagination || { page, limit, total: 0, totalPages: 0 },
          };
          setMessagesCache(prev => ({ ...prev, [conversationId]: formattedResponse }));
          return formattedResponse;
        });
      },
    };
  }, [messagesQueries, messagesCache, requestMessages, wsStatus.connected]);

  // Mutation functions
  const useCreateConversationMutation = useCallback(() => {
    const mutate = async (data: CreateConversationRequest) => {
      setCreateConversationMutation(prev => ({ ...prev, isLoading: true, isError: false }));

      try {
        const response = await wsCreateConversation(data.nurseId, data.nurseName, data.initialMessage);
        const formattedResponse: ConversationResponse = {
          success: true,
          conversation: response.conversation,
        };

        setCreateConversationMutation({
          data: formattedResponse,
          isLoading: false,
          isSuccess: true,
          isError: false,
        });

        // Update conversations cache
        if (conversationsCache && response.conversation) {
          setConversationsCache(prev => prev ? {
            ...prev,
            conversations: [response.conversation, ...prev.conversations],
          } : null);
        }

        return formattedResponse;
      } catch (error) {
        setCreateConversationMutation({
          isLoading: false,
          isSuccess: false,
          isError: true,
          error,
        });
        throw error;
      }
    };

    return [mutate, createConversationMutation] as const;
  }, [wsCreateConversation, createConversationMutation, conversationsCache]);

  const useSendMessageMutation = useCallback(() => {
    const mutate = async (data: { conversationId: string; content: string; type?: 'text' | 'image' | 'file'; metadata?: Record<string, unknown> }) => {
      setSendMessageMutation(prev => ({ ...prev, isLoading: true, isError: false }));

      try {
        const success = sendTextMessage(data.conversationId, data.content, data.metadata);

        if (success) {
          const mockResponse: SendMessageResponse = {
            success: true,
            messageData: {
              id: `${Date.now()}_${Math.random()}`,
              conversationId: data.conversationId,
              senderId: options.userId,
              senderType: options.userType,
              senderName: options.userName,
              content: data.content,
              type: data.type || 'text',
              status: 'sent',
              timestamp: new Date().toISOString(),
              metadata: data.metadata,
            },
          };

          setSendMessageMutation({
            data: mockResponse,
            isLoading: false,
            isSuccess: true,
            isError: false,
          });

          return mockResponse;
        } else {
          throw new Error('Failed to send message');
        }
      } catch (error) {
        setSendMessageMutation({
          isLoading: false,
          isSuccess: false,
          isError: true,
          error,
        });
        throw error;
      }
    };

    return [mutate, sendMessageMutation] as const;
  }, [sendTextMessage, sendMessageMutation, options.userId, options.userType, options.userName]);

  const useMarkMessagesAsReadMutation = useCallback(() => {
    const mutate = async (conversationId: string) => {
      setMarkAsReadMutation(prev => ({ ...prev, isLoading: true, isError: false }));

      try {
        await wsMarkAsRead(conversationId);

        const response = { success: true };
        setMarkAsReadMutation({
          data: response,
          isLoading: false,
          isSuccess: true,
          isError: false,
        });

        return response;
      } catch (error) {
        setMarkAsReadMutation({
          isLoading: false,
          isSuccess: false,
          isError: true,
          error,
        });
        throw error;
      }
    };

    return [mutate, markAsReadMutation] as const;
  }, [wsMarkAsRead, markAsReadMutation]);

  const useUpdateConversationStatusMutation = useCallback(() => {
    const mutate = async (data: { conversationId: string; status: 'active' | 'inactive' | 'archived' }) => {
      setUpdateStatusMutation(prev => ({ ...prev, isLoading: true, isError: false }));

      try {
        const response = await wsUpdateStatus(data.conversationId, data.status);
        const formattedResponse: ConversationResponse = {
          success: true,
          conversation: response.conversation,
        };

        setUpdateStatusMutation({
          data: formattedResponse,
          isLoading: false,
          isSuccess: true,
          isError: false,
        });

        return formattedResponse;
      } catch (error) {
        setUpdateStatusMutation({
          isLoading: false,
          isSuccess: false,
          isError: true,
          error,
        });
        throw error;
      }
    };

    return [mutate, updateStatusMutation] as const;
  }, [wsUpdateStatus, updateStatusMutation]);

  return {
    // WebSocket status
    wsStatus,

    // Query hooks
    useGetConversationsQuery,
    useGetConversationQuery,
    useGetMessagesQuery,

    // Mutation hooks
    useCreateConversationMutation,
    useSendMessageMutation,
    useMarkMessagesAsReadMutation,
    useUpdateConversationStatusMutation,

    // Real-time functions
    sendTextMessage,
    sendTypingIndicator,
    sendReadReceipt,
    joinConversation,
    leaveConversation,
    typingUsers,
  };
};
